interface ApiResponse<T = any> {
  message?: string
  success?: boolean
  data: T
}

type Link = {
  active: boolean
  label: string
  url: string
}

interface ApiResponseWithDataField<T = any> extends ApiResponse<T> {}
interface ApiResponseWithDataPaginationField<T = any> extends ApiResponse<Pagination<T>> {}

// Type alias for paginated API responses
type ApiResponsePagination = ApiResponse<Pagination<any>>
